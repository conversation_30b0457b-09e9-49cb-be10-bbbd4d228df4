# 第1阶段PRD - 基础架构和核心数据管理

## 1. 阶段概述

### 1.1 阶段目标
建立机场地面装卸载作业效能算法库的基础架构，实现核心数据管理功能，专注于地面装卸载作业场景的数据建模和管理。

### 1.2 开发周期
**预计时间**：2个月（8周）
**团队规模**：3-4人（后端开发2人，测试1人，数据库工程师1人）

### 1.3 核心交付物
- 单体应用基础架构
- 数据库设计和初始化
- 装卸载作业场景定义模块
- 装卸载设备配置管理模块
- 人员配置管理模块
- 作业配置方案管理模块
- 核心API接口
- 基础测试框架

## 2. 功能需求详述

### 2.1 装卸载作业场景定义模块

#### 2.1.1 功能描述
支持机场地面装卸载作业场景的定义和管理，为效能计算提供基础数据支撑。

#### 2.1.2 具体需求
**作业类型管理**：
- 支持2种主要作业类型：装载作业、卸载作业
- 装载作业：从机场仓库运输物资到停机坪飞机上
- 卸载作业：从飞机上卸载物资运输到机场仓库
- 每种作业类型包含特定的参数模板和流程定义

**作业环境条件设置**：
- 气象条件：温度、湿度、风速、能见度、降水量
- 机场条件：停机坪规格、仓库位置、通道宽度
- 地面条件：地面平整度、承载能力、摩擦系数
- 照明条件：自然光照、人工照明、夜间作业条件
- 安全条件：安全区域划分、警戒范围、应急通道

**资源约束配置**：
- 装卸载设备：拖车、升降平台、叉车、传送带等数量和规格
- 运输车辆：平板拖车、集装箱拖车等配置
- 人员配置：操作人员、安全监督员、指挥协调员数量
- 时间窗口：作业开始时间、结束时间、允许延误时间

**作业要求定义**：
- 货物信息：集装箱数量、货物重量、体积规格
- 作业距离：仓库到停机坪的距离、运输路径
- 时效性要求：紧急程度、完成时限、优先级
- 质量要求：货物完好率、作业精度、安全标准

**作业约束条件**：
- 设备能力约束：最大载重、作业半径、速度限制
- 人员能力约束：操作技能、体力限制、工作时长
- 安全约束：安全距离、操作规范、应急预案
- 环境约束：天气限制、时间限制、空间限制

#### 2.1.3 数据存储需求
**作业场景基础信息存储**：
- 场景唯一标识和名称
- 作业类型（装载/卸载）
- 场景详细描述
- 环境条件参数（气象、机场、地面、照明、安全）
- 资源约束配置
- 作业要求定义
- 约束条件设置
- 创建者和时间信息
- 场景状态管理

**作业类型模板存储**：
- 作业类型唯一标识和名称
- 作业类型详细描述
- 标准作业流程定义
- 参数模板和约束条件
- 激活状态管理

**环境条件详细存储**：
- 环境条件唯一标识
- 关联的场景信息
- 气象参数详细配置
- 机场条件和地面特征
- 照明和安全条件
- 环境约束参数

### 2.2 装卸载配置管理模块

#### 2.2.1 功能描述
管理机场地面装卸载作业中使用的各类设备和人员配置，专注于地面作业资源管理。

#### 2.2.2 具体需求
**装卸载设备配置管理**：
- 拖车设备：平板拖车、集装箱拖车等地面运输设备
- 升降设备：升降平台、液压升降台、登机桥等
- 装卸设备：叉车、传送带、滚轮系统、吊装设备等
- 辅助设备：牵引车、推拉设备、固定装置等
- 设备规格：载重能力、作业半径、移动速度、功率消耗
- 性能参数：装卸效率、可靠性、维护周期、故障率

**运输车辆配置管理**：
- 车辆类型：平板拖车、集装箱拖车、货物运输车
- 载重参数：最大载重、货厢容积、装载限制
- 性能参数：行驶速度、燃油消耗、机动性能
- 作业参数：装载时间、卸载时间、转运效率
- 维护信息：维护周期、可用率、故障统计

**人员配置管理**：
- 人员类型：设备操作员、装卸工、安全监督员、调度协调员
- 技能等级：初级、中级、高级、专家级
- 能力参数：作业效率、经验系数、疲劳系数
- 班次安排：工作时长、轮班制度、休息要求
- 设备操作：可操作设备类型、操作熟练度、认证资质

#### 2.2.3 数据存储需求
**装卸载设备配置信息存储**：
- 设备唯一标识和型号
- 设备类型分类（拖车、升降设备、装卸设备、辅助设备）
- 设备数量和规格参数
- 性能参数（载重、速度、作业时长、效率系数）
- 运行状态和维护信息
- 创建和更新时间记录

**运输车辆配置信息存储**：
- 车辆唯一标识和型号
- 车辆类型和数量
- 载重能力和容积规格
- 性能参数和作业效率
- 维护计划和可用状态

**人员配置信息存储**：
- 人员配置唯一标识
- 人员类型和数量
- 技能等级和效率参数
- 班次安排和设备分配
- 认证要求和培训记录

### 2.3 作业配置方案管理模块

#### 2.3.1 功能描述
整合装卸载作业相关的各类配置信息，形成完整的作业配置方案，支持方案的版本管理和模板化。

#### 2.3.2 具体需求
**作业配置方案创建**：
- 整合作业场景、设备、车辆、人员等各类配置
- 支持作业配置方案的参数化定义
- 提供配置方案的完整性检查
- 支持配置方案的预览和验证

**版本控制管理**：
- 作业配置方案的版本号管理
- 版本间的差异对比
- 版本回滚和恢复功能
- 版本变更历史记录

**模板化管理**：
- 常用作业配置方案的模板化
- 模板的分类和标签管理（装载模板、卸载模板）
- 基于模板的快速配置创建
- 模板的共享和复用机制

**方案验证功能**：
- 作业配置方案的逻辑一致性检查
- 资源约束的合理性验证
- 参数范围的有效性检查
- 配置冲突的自动检测

#### 2.3.3 数据存储需求
**配置方案基础信息存储**：
- 方案唯一标识和名称
- 方案类型和详细描述
- 版本号和状态管理
- 设备配置列表
- 飞机配置列表
- 人员配置列表
- 场景参数和作业参数
- 创建者和时间信息
- 标签和分类信息

**方案版本控制存储**：
- 版本唯一标识
- 关联的方案信息
- 版本号和变更描述
- 完整的配置数据
- 创建者和时间
- 当前版本标识

**方案模板信息存储**：
- 模板唯一标识和名称
- 模板类型和描述
- 配置模板数据
- 使用次数统计
- 公开状态和创建信息

## 3. 技术实现方案

### 3.1 系统架构设计

#### 3.1.1 单体应用架构设计
**三层应用架构**：
- **表现层**：RESTful API接口，负责请求处理和响应返回
- **业务逻辑层**：作业场景管理、设备配置管理、方案管理等业务模块
- **数据访问层**：数据库操作、缓存管理、文件存储等数据服务

**模块化设计**：
- 采用模块化设计，各业务模块相对独立
- 统一的数据访问接口和业务服务接口
- 支持单机部署和局域网部署

#### 3.1.2 技术栈选择
- **编程语言**：Python 3.11+
- **Web框架**：FastAPI
- **数据库**：SQLite（单机）/ PostgreSQL（局域网）
- **缓存**：内存缓存 / Redis（可选）
- **任务队列**：内置异步任务处理
- **部署方式**：单机可执行文件 / Docker容器
- **API文档**：自动生成OpenAPI文档

#### 3.1.3 项目结构设计
**业务模块组织**：
- 作业场景管理模块：装卸载作业场景定义和参数化
- 设备配置管理模块：装卸载设备和车辆配置管理
- 人员配置管理模块：作业人员配置和技能管理
- 方案管理模块：作业配置方案的创建和管理
- 共享组件：通用工具和基础设施

**数据管理组织**：
- 数据库迁移脚本：版本化的数据库结构变更
- 测试数据种子：用于开发和测试的基础数据
- 数据库模式：完整的数据库结构定义

**测试组织**：
- 单元测试：各模块的独立功能测试
- 集成测试：模块间协作的测试
- 测试数据：标准化的测试用例数据

**部署配置**：
- 单机部署：可执行文件 + 内嵌数据库
- 局域网部署：Docker容器 + 外部数据库
- 配置文件管理和环境变量支持

### 3.2 数据库设计

#### 3.2.1 核心数据表设计
**场景数据表**：
- 存储场景的基本信息：唯一标识、名称、类型、描述
- 记录任务类型和环境条件参数
- 保存资源约束和任务需求配置
- 存储威胁因素和创建者信息
- 支持场景状态管理和时间戳记录

**设备配置数据表**：
- 存储设备的基本信息：唯一标识、类型、型号、数量
- 记录设备规格参数和性能指标
- 保存运行状态和维护信息
- 支持创建和更新时间追踪
- 实现数量约束和状态管理

**飞机配置数据表**：
- 存储飞机的基本信息：唯一标识、机型、数量
- 记录载重能力、航程、速度等性能参数
- 保存装卸时间和机组需求配置
- 存储维护计划和燃油消耗数据
- 支持时间戳和状态追踪

**配置方案数据表**：
- 存储方案的基本信息：唯一标识、名称、类型、描述
- 记录版本号和各类配置关联
- 保存场景参数和作业参数
- 支持标签分类和状态管理
- 实现创建者追踪和时间记录

#### 3.2.2 数据库性能优化设计
**查询性能优化**：
- 场景表按类型和状态建立复合索引，支持快速筛选
- 设备配置表按设备类型建立索引，提高分类查询效率
- 飞机配置表按机型建立索引，优化机型查询性能
- 配置方案表按方案类型建立索引，支持方案分类检索
- 配置方案表的标签字段建立全文索引，支持标签搜索

**数据完整性保证**：
- 设置主键约束确保数据唯一性
- 建立外键关联保证数据一致性
- 添加检查约束验证数据有效性
- 使用时间戳字段追踪数据变更

## 4. API接口详细规范

### 4.1 作业场景管理API

#### 4.1.1 创建装卸载作业场景
**接口地址**：`POST /api/v1/loading-scenarios`

**业务功能**：创建新的机场地面装卸载作业场景，定义完整的作业参数和环境条件

**请求参数说明**：
- 场景基本信息：场景名称、作业类型（装载/卸载）、描述
- 环境条件配置：气象参数、机场条件、地面条件、照明条件、安全条件
- 资源约束设置：设备数量限制、车辆数量限制、人员数量限制、时间窗口
- 作业需求定义：集装箱数量、货物重量、作业距离、时效要求
- 约束条件设置：设备能力约束、人员能力约束、安全约束、环境约束

**响应内容**：
- 场景唯一标识：用于后续操作的场景ID
- 场景基本信息：名称和状态确认
- 创建时间：场景创建的时间戳
- 操作结果：创建成功或失败的状态信息

#### 4.1.2 获取装卸载作业场景列表
**接口地址**：`GET /api/v1/loading-scenarios`

**业务功能**：分页查询装卸载作业场景列表，支持多种筛选条件

**查询参数说明**：
- 分页参数：页码和每页数量控制
- 作业类型筛选：按装载作业和卸载作业过滤
- 状态筛选：按场景状态过滤
- 关键词搜索：支持场景名称和描述的模糊搜索

**响应内容**：
- 场景列表：包含场景基本信息的数组
- 分页信息：当前页码、每页数量、总数、总页数
- 场景详情：每个场景的ID、名称、作业类型、状态、时间等信息

### 4.2 装卸载设备配置管理API

#### 4.2.1 创建装卸载设备配置
**接口地址**：`POST /api/v1/loading-equipment-configs`

**业务功能**：创建新的装卸载设备配置，定义设备的基本信息和性能参数

**请求参数说明**：
- 设备基本信息：设备类型、型号、数量
- 规格参数：最大载重、装卸速度、作业半径、功耗
- 性能参数：单位作业时长、效率系数、维护间隔
- 状态信息：运行状态、维护信息

**业务价值**：为装卸载作业效能计算提供准确的设备能力数据，支持资源配置优化

#### 4.2.2 获取装卸载设备配置列表
**接口地址**：`GET /api/v1/loading-equipment-configs`

**业务功能**：查询装卸载设备配置列表，支持按类型和状态筛选

**查询参数说明**：
- 设备类型筛选：按拖车、升降设备、装卸设备、辅助设备分类查询
- 运行状态筛选：按可用、维修、故障等状态过滤
- 分页参数：支持大量设备配置的分页浏览

### 4.3 作业配置方案管理API

#### 4.3.1 创建作业配置方案
**接口地址**：`POST /api/v1/loading-configuration-schemes`

**业务功能**：创建完整的装卸载作业配置方案，整合设备、车辆、人员等各类配置

**请求参数说明**：
- 方案基本信息：方案名称、作业类型、描述
- 设备配置关联：关联的装卸载设备配置ID、数量、分配优先级
- 车辆配置关联：关联的运输车辆配置ID、数量、任务分配
- 人员配置定义：人员类型、数量、技能等级、班次安排
- 作业参数设置：最大作业时长、安全裕度、效率目标
- 标签分类：便于方案分类和检索的标签（装载方案、卸载方案等）

**业务价值**：提供完整的装卸载作业资源配置方案，支持效能计算和方案比较分析

## 5. 测试策略和验收标准

### 5.1 测试策略

#### 5.1.1 单元测试
- **覆盖率要求**：≥90%
- **测试框架**：pytest + pytest-cov
- **测试内容**：
  - 数据模型验证
  - 业务逻辑函数
  - 工具函数和辅助方法
  - 异常处理逻辑

#### 5.1.2 集成测试
- **测试范围**：API接口、数据库操作、服务间通信
- **测试工具**：pytest + httpx + testcontainers
- **测试环境**：Docker容器化测试环境

#### 5.1.3 性能测试
- **响应时间要求**：
  - 简单查询API：< 200ms
  - 复杂查询API：< 1s
  - 数据创建API：< 500ms
- **并发测试**：支持50个并发请求
- **数据量测试**：支持10万条记录的查询和操作

### 5.2 验收标准

#### 5.2.1 功能验收标准
- 场景管理功能完整实现，支持CRUD操作
- 设备配置管理功能完整实现，支持多种设备类型
- 飞机配置管理功能完整实现，支持主要机型
- 配置方案管理功能完整实现，支持版本控制
- 所有API接口正常工作，返回格式符合规范

#### 5.2.2 性能验收标准
- API响应时间满足要求
- 数据库查询性能优化到位
- 系统支持预期的并发用户数
- 内存使用控制在合理范围内

#### 5.2.3 质量验收标准
- 代码覆盖率达到90%以上
- 所有测试用例通过
- 代码质量检查通过（flake8、black、mypy）
- 文档完整，包含API文档和开发文档

## 6. 风险评估和应对措施

### 6.1 技术风险

#### 6.1.1 数据库性能风险
**风险描述**：大量JSONB字段可能影响查询性能
**应对措施**：
- 合理设计索引，特别是GIN索引
- 对热点查询进行性能测试和优化
- 考虑数据分区策略

#### 6.1.2 微服务复杂性风险
**风险描述**：微服务架构增加系统复杂性
**应对措施**：
- 采用成熟的微服务框架和工具
- 完善的服务监控和日志系统
- 详细的服务间接口文档

### 6.2 进度风险

#### 6.2.1 需求变更风险
**风险描述**：需求变更可能影响开发进度
**应对措施**：
- 需求冻结机制，重大变更需评估影响
- 预留20%的缓冲时间
- 采用敏捷开发方法，快速响应变更

#### 6.2.2 技术难点风险
**风险描述**：复杂数据结构设计可能遇到技术难点
**应对措施**：
- 提前进行技术预研和原型验证
- 寻求技术专家支持
- 制定备选技术方案

## 7. 开发时间线和里程碑

### 7.1 第1-3周：基础架构搭建
- **Week 1-2**：项目初始化、数据库设计、单体应用框架搭建
- **Week 3**：核心数据模型实现、基础API框架

### 7.2 第4-6周：装卸载作业场景管理功能开发
- **Week 4-5**：作业场景定义模块、参数化配置功能
- **Week 6**：环境条件管理、场景验证功能

### 7.3 第7-8周：配置管理功能开发
- **Week 7**：装卸载设备配置管理、运输车辆配置管理
- **Week 8**：人员配置管理、作业配置方案管理、系统测试

### 7.4 关键里程碑
- **M1（第3周末）**：基础架构完成，核心数据模型可用
- **M2（第6周末）**：装卸载作业场景管理功能完成
- **M3（第8周末）**：第一阶段功能全部完成，通过验收测试

---

*第1阶段PRD文档 - 版本2.0*
*修订日期：2025年8月4日*
*专注机场地面装卸载作业，支持单机/局域网部署*
